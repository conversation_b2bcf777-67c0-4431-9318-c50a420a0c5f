<?php
if (!defined('ABSPATH')) {
    exit;
}

// Get team data
$team_id = isset($_GET['team_id']) ? intval($_GET['team_id']) : 0;
$team = $this->get_team_by_id($team_id);

if (!$team) {
    echo '<div class="wrap"><div class="notice notice-error"><p>لم يتم العثور على الفريق المطلوب.</p></div></div>';
    return;
}

// Check if current user is team leader or admin
$current_user_id = get_current_user_id();
if (!current_user_can('manage_options') && $team->created_by != $current_user_id) {
    wp_die('ليس لديك صلاحية لتعديل هذا الفريق');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && check_admin_referer('update_team_' . $team_id)) {
    // Process and update team data
    $this->update_team($team_id, $_POST);
    $team = $this->get_team_by_id($team_id); // Refresh team data
    echo '<div class="notice notice-success"><p>تم تحديث بيانات الفريق بنجاح.</p></div>';
}
?>

<div class="wrap">
    <h1>تعديل الفريق: <?php echo esc_html($team->name); ?></h1>
    
    <div class="team-edit-tabs">
        <h2 class="nav-tab-wrapper">
            <a href="#team-info" class="nav-tab nav-tab-active">معلومات الفريق</a>
            <a href="#team-members" class="nav-tab">إدارة الأعضاء</a>
            <a href="#team-settings" class="nav-tab">الإعدادات</a>
        </h2>
        
        <form method="post" enctype="multipart/form-data" class="team-edit-form">
            <?php wp_nonce_field('update_team_' . $team_id); ?>
            
            <div id="team-info" class="tab-content active">
                <table class="form-table">
                    <tr>
                        <th><label for="team_name">اسم الفريق</label></th>
                        <td>
                            <input type="text" name="team_name" id="team_name" class="regular-text"
                                   value="<?php echo esc_attr($team->name); ?>" required>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="team_slug">رابط الفريق</label></th>
                        <td>
                            <div class="slug-input-wrapper">
                                <span class="slug-prefix"><?php echo home_url('/teams/'); ?></span>
                                <input type="text" name="team_slug" id="team_slug" class="regular-text"
                                       value="<?php echo esc_attr($team->slug); ?>"
                                       pattern="[a-z0-9\-]+"
                                       title="يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط">
                            </div>
                            <p class="description">
                                رابط فريقك الحالي: <?php echo home_url('/teams/' . $team->slug); ?><br>
                                يُسمح بالأحرف الإنجليزية الصغيرة (a-z) والأرقام (0-9) والشرطة (-) فقط.
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="team_description">وصف الفريق</label></th>
                        <td>
                            <?php
                            wp_editor(
                                wp_kses_post($team->description),
                                'team_description',
                                array(
                                    'textarea_name' => 'team_description',
                                    'media_buttons' => false,
                                    'textarea_rows' => 10,
                                    'teeny' => true
                                )
                            );
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th>شعار الفريق الحالي</th>
                        <td>
                            <?php if ($team->logo_url): ?>
                                <img src="<?php echo esc_url($team->logo_url); ?>" style="max-width: 150px; height: auto; display: block; margin-bottom: 10px;">
                            <?php endif; ?>
                            <input type="file" name="team_logo" id="team_logo" accept="image/*">
                            <p class="description">الحجم الموصى به 200 × 200 بكسل</p>
                        </td>
                    </tr>
                    <tr>
                        <th>صورة الغلاف الحالية</th>
                        <td>
                            <?php if ($team->cover_url): ?>
                                <img src="<?php echo esc_url($team->cover_url); ?>" style="max-width: 100%; height: auto; max-height: 200px; display: block; margin-bottom: 10px;">
                            <?php endif; ?>
                            <input type="file" name="team_cover" id="team_cover" accept="image/*">
                            <p class="description">الحجم الموصى به 1200 × 400 بكسل</p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div id="team-members" class="tab-content">
                <h3>أعضاء الفريق</h3>
                <div class="team-members-list">
                    <?php
                    $members = $this->get_team_members($team_id);
                    foreach ($members as $member) {
                        $user = get_userdata($member->user_id);
                        $role_name = $this->get_role_name($member->role);
                        ?>
                        <div class="team-member-row">
                            <div class="member-avatar"><?php echo get_avatar($user->ID, 50); ?></div>
                            <div class="member-info">
                                <div class="member-name"><?php echo esc_html($user->display_name); ?></div>
                                <div class="member-email"><?php echo esc_html($user->user_email); ?></div>
                            </div>
                            <div class="member-role">
                                <select name="member_roles[<?php echo $member->user_id; ?>]">
                                    <?php foreach ($this->get_available_roles() as $role => $label): ?>
                                        <option value="<?php echo $role; ?>" <?php selected($member->role, $role); ?>>
                                            <?php echo $label; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="member-actions">
                                <?php if ($member->user_id != $team->created_by): ?>
                                    <button type="button" class="button remove-member" data-user-id="<?php echo $member->user_id; ?>">
                                        إزالة
                                    </button>
                                <?php else: ?>
                                    <span class="team-leader-badge">قائد الفريق</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                </div>

                <?php
                // Get pending invitations
                global $wpdb;
                $pending_invitations = $wpdb->get_results($wpdb->prepare(
                    "SELECT i.*, u.display_name, u.user_email
                     FROM {$wpdb->prefix}team_invitations i
                     LEFT JOIN {$wpdb->users} u ON i.user_id = u.ID
                     WHERE i.team_id = %d AND i.status = 'pending' AND i.expires_at > NOW()
                     ORDER BY i.created_at DESC",
                    $team_id
                ));

                if (!empty($pending_invitations)): ?>
                    <h3 style="margin-top: 30px;">الدعوات المعلقة</h3>
                    <div class="pending-invitations-list">
                        <?php foreach ($pending_invitations as $invitation): ?>
                            <div class="invitation-row" data-invitation-id="<?php echo $invitation->id; ?>">
                                <div class="invitation-info">
                                    <div class="invitation-user">
                                        <strong><?php echo esc_html($invitation->display_name); ?></strong>
                                        <span class="invitation-email"><?php echo esc_html($invitation->user_email); ?></span>
                                    </div>
                                    <div class="invitation-details">
                                        <span class="invitation-role">الدور: <?php echo esc_html($this->get_role_name($invitation->role)); ?></span>
                                        <span class="invitation-date">أُرسلت في: <?php echo date_i18n('j F Y', strtotime($invitation->created_at)); ?></span>
                                        <span class="invitation-expires">تنتهي في: <?php echo date_i18n('j F Y', strtotime($invitation->expires_at)); ?></span>
                                    </div>
                                </div>
                                <div class="invitation-status">
                                    <span class="status-badge pending">في انتظار الرد</span>
                                </div>
                                <div class="invitation-actions">
                                    <button type="button" class="button cancel-invitation" data-invitation-id="<?php echo $invitation->id; ?>">
                                        إلغاء الدعوة
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <h3 style="margin-top: 30px;">دعوة أعضاء جدد</h3>
                <div class="add-member-section">
                    <input type="email" id="new_member_email" placeholder="البريد الإلكتروني للعضو الجديد" class="regular-text">
                    <select id="new_member_role">
                        <?php foreach ($this->get_available_roles() as $role => $label): ?>
                            <?php if ($role !== 'leader'): ?>
                                <option value="<?php echo $role; ?>"><?php echo $label; ?></option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                    <button type="button" id="add_member_btn" class="button button-primary">إرسال دعوة</button>
                </div>
            </div>
            
            <div id="team-settings" class="tab-content">
                <h3>إعدادات الفريق</h3>
                <table class="form-table">
                    <tr>
                        <th><label for="team_website">الموقع الإلكتروني</label></th>
                        <td>
                            <input type="url" name="team_website" id="team_website" class="regular-text" 
                                   value="<?php echo esc_url($team->website_url); ?>">
                        </td>
                    </tr>
                    <tr>
                        <th><label for="team_facebook">فيسبوك</label></th>
                        <td>
                            <input type="url" name="team_facebook" id="team_facebook" class="regular-text" 
                                   value="<?php echo esc_url($team->facebook_url); ?>">
                        </td>
                    </tr>
                    <tr>
                        <th><label for="team_twitter">تويتر</label></th>
                        <td>
                            <input type="url" name="team_twitter" id="team_twitter" class="regular-text" 
                                   value="<?php echo esc_url($team->twitter_url); ?>">
                        </td>
                    </tr>
                    <tr>
                        <th><label for="team_discord">ديسكورد</label></th>
                        <td>
                            <input type="url" name="team_discord" id="team_discord" class="regular-text" 
                                   value="<?php echo esc_url($team->discord_url); ?>">
                        </td>
                    </tr>
                    <tr>
                        <th><label for="team_privacy">خصوصية الفريق</label></th>
                        <td>
                            <select name="team_privacy" id="team_privacy">
                                <option value="public" <?php selected($team->privacy, 'public'); ?>>عام (يمكن للجميع رؤية الفريق وأعضائه)</option>
                                <option value="private" <?php selected($team->privacy, 'private'); ?>>خاص (يمكن للجميع رؤية الفريق ولكن الأعضاء فقط هم من يمكنهم رؤية الأعضاء)</option>
                                <option value="hidden" <?php selected($team->privacy, 'hidden'); ?>>مخفي (لا يمكن لأحد رؤية الفريق سوى الأعضاء)</option>
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="submit-buttons">
                <button type="submit" name="submit" class="button button-primary">حفظ التغييرات</button>
                <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="button">رجوع</a>
            </div>
        </form>
    </div>
</div>

<script>
// Define AJAX variables if not already defined
if (typeof ajaxurl === 'undefined') {
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
}

// Define team system AJAX object if not already defined
if (typeof teamSystemAjax === 'undefined') {
    var teamSystemAjax = {
        nonce: '<?php echo wp_create_nonce('team_system_nonce'); ?>',
        ajaxurl: ajaxurl
    };
}

jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        // Update active tab
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        // Show target tab content
        $('.tab-content').removeClass('active');
        $(target).addClass('active');
    });
    
    // Add member
    $('#add_member_btn').on('click', function() {
        var email = $('#new_member_email').val().trim();
        var role = $('#new_member_role').val();
        
        if (!email) {
            alert('الرجاء إدخال بريد إلكتروني صحيح');
            return;
        }
        
        // Send AJAX request to add member
        $.post(ajaxurl, {
            action: 'team_system_add_member',
            team_id: <?php echo $team_id; ?>,
            email: email,
            role: role,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                // Show success message
                alert(response.data.message || 'تم إرسال الدعوة بنجاح');
                // Clear form
                $('#new_member_email').val('');
                // Reload to show pending invitation
                location.reload();
            } else {
                alert(response.data.message || 'حدث خطأ ما');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });
    
    // Remove member
    $('.remove-member').on('click', function() {
        if (!confirm('هل أنت متأكد من إزالة هذا العضو من الفريق؟')) {
            return;
        }
        
        var $row = $(this).closest('.team-member-row');
        var userId = $(this).data('user-id');
        
        // Send AJAX request to remove member
        $.post(ajaxurl, {
            action: 'team_system_remove_member',
            team_id: <?php echo $team_id; ?>,
            user_id: userId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $row.fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert(response.data.message || 'حدث خطأ ما');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Cancel invitation
    $('.cancel-invitation').on('click', function() {
        if (!confirm('هل أنت متأكد من إلغاء هذه الدعوة؟')) {
            return;
        }

        var $row = $(this).closest('.invitation-row');
        var invitationId = $(this).data('invitation-id');

        // Send AJAX request to cancel invitation
        $.post(ajaxurl, {
            action: 'team_system_cancel_invitation',
            invitation_id: invitationId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $row.fadeOut(300, function() {
                    $(this).remove();
                });
                alert(response.data.message || 'تم إلغاء الدعوة');
            } else {
                alert(response.data.message || 'حدث خطأ ما');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Team slug validation for edit form
    $('#team_slug').on('input', function() {
        var slug = $(this).val().toLowerCase();
        $(this).val(slug); // Force lowercase

        // Basic validation
        if (slug && !/^[a-z0-9\-]+$/.test(slug)) {
            $(this).css('border-color', '#d63638');
        } else {
            $(this).css('border-color', '');
        }
    });
});
</script>

<style>
.slug-input-wrapper {
    display: flex;
    align-items: center;
    max-width: 600px;
}
.slug-prefix {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-right: none;
    padding: 8px 12px;
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}
#team_slug {
    border-left: none !important;
    flex: 1;
}

/* Pending Invitations Styling */
.pending-invitations-list {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.invitation-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.invitation-row:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.invitation-info {
    flex: 1;
}

.invitation-user {
    margin-bottom: 8px;
}

.invitation-user strong {
    color: #2c3e50;
    font-size: 16px;
}

.invitation-email {
    color: #7f8c8d;
    font-size: 14px;
    margin-left: 10px;
}

.invitation-details {
    display: flex;
    gap: 15px;
    font-size: 13px;
    color: #6c757d;
}

.invitation-role {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.invitation-status {
    margin: 0 15px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.invitation-actions .button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.invitation-actions .button:hover {
    background: #c82333;
}

/* Add member section styling */
.add-member-section {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.add-member-section input[type="email"] {
    flex: 2;
}

.add-member-section select {
    flex: 1;
}

.add-member-section .button {
    flex: 0 0 auto;
}
</style>

<style>
.team-edit-tabs {
    margin-top: 20px;
}

.tab-content {
    display: none;
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-top: none;
    margin-top: -1px;
}

.tab-content.active {
    display: block;
}

.team-member-row {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.team-member-row:last-child {
    border-bottom: none;
}

.member-avatar {
    margin-left: 15px;
}

.member-avatar img {
    border-radius: 50%;
}

.member-info {
    flex: 1;
}

.member-name {
    font-weight: 600;
    margin: 0 0 3px 0;
}

.member-email {
    color: #646970;
    font-size: 0.9em;
}

.member-role {
    min-width: 150px;
    margin: 0 15px;
}

.member-role select {
    width: 100%;
}

.team-leader-badge {
    background: #f0f0f1;
    color: #1d2327;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.85em;
}

.add-member-section {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.add-member-section input[type="email"] {
    flex: 1;
    max-width: 300px;
}

.submit-buttons {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}
</style>