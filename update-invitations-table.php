<?php
/**
 * Database update script for Team System - Invitations Table
 * Run this file once to add the team_invitations table
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

global $wpdb;

$table_invitations = $wpdb->prefix . 'team_invitations';

echo "<h2>Team System Database Update - Invitations Table</h2>";
echo "<p>Creating team invitations table...</p>";

// Check if table already exists
if ($wpdb->get_var("SHOW TABLES LIKE '$table_invitations'") == $table_invitations) {
    echo "<p style='color: blue;'>- Table already exists: $table_invitations</p>";
} else {
    // Create invitations table
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_invitations (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        team_id bigint(20) NOT NULL,
        email varchar(100) NOT NULL,
        user_id bigint(20) DEFAULT NULL,
        role varchar(50) NOT NULL,
        token varchar(100) NOT NULL,
        invited_by bigint(20) NOT NULL,
        created_at datetime NOT NULL,
        expires_at datetime NOT NULL,
        status varchar(20) DEFAULT 'pending',
        PRIMARY KEY (id),
        UNIQUE KEY token (token),
        KEY team_id (team_id),
        KEY user_id (user_id),
        KEY status (status),
        KEY expires_at (expires_at)
    ) $charset_collate;";
    
    $result = $wpdb->query($sql);
    
    if ($result !== false) {
        echo "<p style='color: green;'>✓ Created table: $table_invitations</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create table: $table_invitations</p>";
        echo "<p style='color: red;'>Error: " . $wpdb->last_error . "</p>";
    }
}

// Also check if team_members table has status column
$table_members = $wpdb->prefix . 'team_members';
$status_column = $wpdb->get_results("SHOW COLUMNS FROM $table_members LIKE 'status'");

if (empty($status_column)) {
    echo "<p>Adding status column to team_members table...</p>";
    $result = $wpdb->query("ALTER TABLE $table_members ADD COLUMN status VARCHAR(20) DEFAULT 'active'");
    
    if ($result !== false) {
        echo "<p style='color: green;'>✓ Added status column to team_members table</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Could not add status column: " . $wpdb->last_error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>- Status column already exists in team_members table</p>";
}

echo "<p style='color: green; font-weight: bold;'>Database update completed!</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>Test the invitation system by going to a team edit page</li>";
echo "<li>Try sending an invitation to a user</li>";
echo "<li>Check the main teams page for invitation notifications</li>";
echo "<li>Delete this file for security reasons</li>";
echo "</ul>";

echo "<p><strong>Note:</strong> The invitation system is now ready to use!</p>";
?>
